{"info": {"_postman_id": "kortana-ai-api-collection", "name": "Kortana AI - Instagram Chatbot API", "description": "Complete API collection for testing Kortana AI Instagram Chatbot endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "kortana-ai"}, "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/health", "host": ["{{base_url}}"], "path": ["api", "health"]}}, "response": []}, {"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"Password123!\",\n    \"password_confirmation\": \"Password123!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}}, "response": []}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.token) {", "        pm.environment.set('auth_token', response.data.token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"Password123!\",\n    \"remember\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}}, "response": []}, {"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/auth/user", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "user"]}}, "response": []}, {"name": "Logout User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/auth/logout", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "logout"]}}, "response": []}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/forgot-password", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "forgot-password"]}}, "response": []}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"otp\": \"123456\",\n    \"password\": \"NewPassword123!\",\n    \"password_confirmation\": \"NewPassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/reset-password", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "reset-password"]}}, "response": []}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"otp\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/otp-verify", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "otp-verify"]}}, "response": []}]}, {"name": "Social Media", "item": [{"name": "Send Message", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"platform\": \"instagram\",\n    \"recipient_id\": \"1234567890\",\n    \"message\": \"Hello! How can I help you today?\",\n    \"message_type\": \"text\",\n    \"metadata\": {\n        \"campaign_id\": \"welcome_campaign\",\n        \"tags\": [\"greeting\", \"automated\"],\n        \"priority\": \"normal\"\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v1/social/send-message", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "send-message"]}}, "response": []}, {"name": "Get Instagram Messages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/social/instagram/messages?per_page=20", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "instagram", "messages"], "query": [{"key": "per_page", "value": "20"}]}}, "response": []}, {"name": "Get Instagram Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/social/instagram/stats", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "instagram", "stats"]}}, "response": []}, {"name": "Get Conversation History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/social/instagram/conversations/history?conversation_id=conv_123&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "instagram", "conversations", "history"], "query": [{"key": "conversation_id", "value": "conv_123"}, {"key": "limit", "value": "20"}]}}, "response": []}, {"name": "Get Active Conversations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/social/instagram/conversations/active", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "instagram", "conversations", "active"]}}, "response": []}, {"name": "Get Pending Messages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/social/instagram/messages/pending", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "instagram", "messages", "pending"]}}, "response": []}, {"name": "Get Hourly Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/social/instagram/analytics/hourly", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "instagram", "analytics", "hourly"]}}, "response": []}]}, {"name": "Business Configuration", "item": [{"name": "Get All Configurations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/business/configs", "host": ["{{base_url}}"], "path": ["api", "v1", "business", "configs"]}}, "response": []}, {"name": "Get Business Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/business/info", "host": ["{{base_url}}"], "path": ["api", "v1", "business", "info"]}}, "response": []}, {"name": "Update Business Info", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"My Business\",\n    \"type\": \"Restaurant\",\n    \"description\": \"A great place to eat\",\n    \"specialties\": [\"Italian\", \"Pizza\", \"Pasta\"],\n    \"contact\": {\n        \"phone\": \"+1234567890\",\n        \"email\": \"<EMAIL>\",\n        \"address\": \"123 Main St, City, State 12345\"\n    },\n    \"social_media\": {\n        \"instagram\": \"@mybusiness\",\n        \"facebook\": \"mybusiness\",\n        \"website\": \"https://mybusiness.com\"\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v1/business/info", "host": ["{{base_url}}"], "path": ["api", "v1", "business", "info"]}}, "response": []}, {"name": "Get Services", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/business/services", "host": ["{{base_url}}"], "path": ["api", "v1", "business", "services"]}}, "response": []}, {"name": "Update Services", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"services\": {\n        \"consultation\": {\n            \"name\": \"Business Consultation\",\n            \"duration\": 60,\n            \"price\": 100.00,\n            \"description\": \"Professional business consultation\",\n            \"category\": \"consulting\"\n        },\n        \"training\": {\n            \"name\": \"Staff Training\",\n            \"duration\": 120,\n            \"price\": 200.00,\n            \"description\": \"Comprehensive staff training program\",\n            \"category\": \"training\"\n        }\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v1/business/services", "host": ["{{base_url}}"], "path": ["api", "v1", "business", "services"]}}, "response": []}]}, {"name": "Agent Configuration", "item": [{"name": "Get Agent Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/agent/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "agent", "profile"]}}, "response": []}, {"name": "Update Agent Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"agent_type\": \"Customer Service Assistant\",\n    \"agent_name\": \"<PERSON>rtana\",\n    \"agent_gender\": \"female\",\n    \"personality\": \"friendly\",\n    \"shop_type\": \"Restaurant\",\n    \"response_tone\": \"helpful\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/agent/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "agent", "profile"]}}, "response": []}]}, {"name": "Chatbot Configuration", "item": [{"name": "<PERSON> Chatbot Prompts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/chatbot/prompts", "host": ["{{base_url}}"], "path": ["api", "v1", "chatbot", "prompts"]}}, "response": []}, {"name": "Update Chatbot Prompts", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"prompts\": {\n        \"greeting\": \"Welcome to our restaurant! How can I help you today?\",\n        \"services_inquiry\": \"We offer amazing Italian cuisine, pizza, and pasta. What interests you?\",\n        \"appointment_prompt\": \"I can help you make a reservation. When would you like to dine with us?\",\n        \"pricing_info\": \"Our menu items range from $10-30. Would you like to see our full menu?\",\n        \"location_info\": \"We're located at 123 Main St. Would you like directions?\"\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v1/chatbot/prompts", "host": ["{{base_url}}"], "path": ["api", "v1", "chatbot", "prompts"]}}, "response": []}]}, {"name": "Knowledge Base", "item": [{"name": "Upload Knowledge File", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}, {"key": "description", "value": "Business knowledge document", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/v1/knowledge/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "knowledge", "upload"]}}, "response": []}, {"name": "Get Knowledge Files", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/knowledge/files?per_page=15", "host": ["{{base_url}}"], "path": ["api", "v1", "knowledge", "files"], "query": [{"key": "per_page", "value": "15"}]}}, "response": []}, {"name": "Get Knowledge File Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/knowledge/files/1", "host": ["{{base_url}}"], "path": ["api", "v1", "knowledge", "files", "1"]}}, "response": []}, {"name": "Delete Knowledge File", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/knowledge/files/1", "host": ["{{base_url}}"], "path": ["api", "v1", "knowledge", "files", "1"]}}, "response": []}, {"name": "Search Knowledge Base", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"query\": \"restaurant menu prices\",\n    \"limit\": 5\n}"}, "url": {"raw": "{{base_url}}/api/v1/knowledge/search", "host": ["{{base_url}}"], "path": ["api", "v1", "knowledge", "search"]}}, "response": []}, {"name": "Get Knowledge Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/knowledge/stats", "host": ["{{base_url}}"], "path": ["api", "v1", "knowledge", "stats"]}}, "response": []}, {"name": "Reprocess Failed File", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/knowledge/files/1/reprocess", "host": ["{{base_url}}"], "path": ["api", "v1", "knowledge", "files", "1", "reprocess"]}}, "response": []}]}, {"name": "Scheduling", "item": [{"name": "Get Available Slots", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/scheduling/slots/available?date=2024-01-15", "host": ["{{base_url}}"], "path": ["api", "v1", "scheduling", "slots", "available"], "query": [{"key": "date", "value": "2024-01-15"}]}}, "response": []}, {"name": "Get Next Available Slot", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/scheduling/slots/next-available", "host": ["{{base_url}}"], "path": ["api", "v1", "scheduling", "slots", "next-available"]}}, "response": []}, {"name": "Get Time Slots Config", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/scheduling/time-slots/config", "host": ["{{base_url}}"], "path": ["api", "v1", "scheduling", "time-slots", "config"]}}, "response": []}, {"name": "Update Time Slots Config", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"global_time_periods\": {\n        \"morning\": [\"09:00\", \"12:00\"],\n        \"afternoon\": [\"13:00\", \"17:00\"],\n        \"evening\": [\"18:00\", \"21:00\"]\n    },\n    \"slot_duration_minutes\": 30,\n    \"buffer_minutes\": 15,\n    \"max_advance_days\": 30,\n    \"min_advance_hours\": 2\n}"}, "url": {"raw": "{{base_url}}/api/v1/scheduling/time-slots/config", "host": ["{{base_url}}"], "path": ["api", "v1", "scheduling", "time-slots", "config"]}}, "response": []}, {"name": "Get Weekend Settings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/scheduling/weekend/settings", "host": ["{{base_url}}"], "path": ["api", "v1", "scheduling", "weekend", "settings"]}}, "response": []}, {"name": "Update Weekend Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"saturday_enabled\": true,\n    \"sunday_enabled\": false,\n    \"saturday_hours\": {\n        \"start\": \"10:00\",\n        \"end\": \"16:00\"\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v1/scheduling/weekend/settings", "host": ["{{base_url}}"], "path": ["api", "v1", "scheduling", "weekend", "settings"]}}, "response": []}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "Get All Meta Tokens", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/meta/tokens", "host": ["{{base_url}}"], "path": ["api", "v1", "meta", "tokens"]}}, "response": []}, {"name": "Create <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"access_token\": \"IGAAQ6Fmen2tlBZAE5RUEFSR3BhWTNGTzNPdDB0ME9iMlJ4ZAFV1S1lwQkFXTWRGRkF6VmJHemVXT1dVY3lqQVNVSHB4b1BySkx3VnFVRUpFQ19XMlE5MTVkd1hMYTZAZASzFRT2JfRVRpbmw4MDlOaTVSU2FFeEtHeXpVcXF1OE9YNAZDZD\",\n    \"page_id\": \"17841475512211505\",\n    \"page_name\": \"My Business Page\",\n    \"expires_at\": \"2024-12-31T23:59:59Z\",\n    \"scopes\": [\"pages_messaging\", \"pages_read_engagement\"]\n}"}, "url": {"raw": "{{base_url}}/api/v1/meta/tokens", "host": ["{{base_url}}"], "path": ["api", "v1", "meta", "tokens"]}}, "response": []}, {"name": "Get Meta Token Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/meta/tokens/1", "host": ["{{base_url}}"], "path": ["api", "v1", "meta", "tokens", "1"]}}, "response": []}, {"name": "Update <PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"page_name\": \"Updated Business Page\",\n    \"expires_at\": \"2025-12-31T23:59:59Z\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/meta/tokens/1", "host": ["{{base_url}}"], "path": ["api", "v1", "meta", "tokens", "1"]}}, "response": []}, {"name": "Delete Meta Token", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/meta/tokens/1", "host": ["{{base_url}}"], "path": ["api", "v1", "meta", "tokens", "1"]}}, "response": []}, {"name": "Veri<PERSON> Met<PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/meta/tokens/1/verify", "host": ["{{base_url}}"], "path": ["api", "v1", "meta", "tokens", "1", "verify"]}}, "response": []}, {"name": "Get Current Page Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/meta/current-page", "host": ["{{base_url}}"], "path": ["api", "v1", "meta", "current-page"]}}, "response": []}]}, {"name": "Instagram Authentication", "item": [{"name": "Instagram OAuth Redirect", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/instagram", "host": ["{{base_url}}"], "path": ["api", "auth", "instagram"]}}, "response": []}, {"name": "Instagram OAuth <PERSON>back", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/instagram/callback?code=AUTH_CODE&state=STATE_VALUE", "host": ["{{base_url}}"], "path": ["api", "auth", "instagram", "callback"], "query": [{"key": "code", "value": "AUTH_CODE"}, {"key": "state", "value": "STATE_VALUE"}]}}, "response": []}, {"name": "Instagram Connection Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/instagram/status", "host": ["{{base_url}}"], "path": ["api", "auth", "instagram", "status"]}}, "response": []}, {"name": "Disconnect Instagram", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/auth/instagram/disconnect", "host": ["{{base_url}}"], "path": ["api", "auth", "instagram", "disconnect"]}}, "response": []}, {"name": "Refresh Instagram Token", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/auth/instagram/refresh", "host": ["{{base_url}}"], "path": ["api", "auth", "instagram", "refresh"]}}, "response": []}]}, {"name": "Webhooks", "item": [{"name": "Instagram Webhook Verification", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/webhooks/instagram?hub.mode=subscribe&hub.challenge=CHALLENGE_VALUE&hub.verify_token=kortana_webhook_verify_2024", "host": ["{{base_url}}"], "path": ["api", "webhooks", "instagram"], "query": [{"key": "hub.mode", "value": "subscribe"}, {"key": "hub.challenge", "value": "CHALLENGE_VALUE"}, {"key": "hub.verify_token", "value": "kort<PERSON>_webhook_verify_2024"}]}}, "response": []}, {"name": "Instagram Webhook Handler", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Hub-Signature-256", "value": "sha256=SIGNATURE_HASH"}], "body": {"mode": "raw", "raw": "{\n    \"object\": \"page\",\n    \"entry\": [\n        {\n            \"id\": \"17841475512211505\",\n            \"time\": 1234567890,\n            \"messaging\": [\n                {\n                    \"sender\": {\n                        \"id\": \"1234567890\"\n                    },\n                    \"recipient\": {\n                        \"id\": \"17841475512211505\"\n                    },\n                    \"timestamp\": 1234567890,\n                    \"message\": {\n                        \"mid\": \"message_id\",\n                        \"text\": \"Hello, I need help with my order\"\n                    }\n                }\n            ]\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/webhooks/instagram", "host": ["{{base_url}}"], "path": ["api", "webhooks", "instagram"]}}, "response": []}, {"name": "Facebook Webhook Verification", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/webhooks/facebook?hub.mode=subscribe&hub.challenge=CHALLENGE_VALUE&hub.verify_token=kortana_webhook_verify_2024", "host": ["{{base_url}}"], "path": ["api", "webhooks", "facebook"], "query": [{"key": "hub.mode", "value": "subscribe"}, {"key": "hub.challenge", "value": "CHALLENGE_VALUE"}, {"key": "hub.verify_token", "value": "kort<PERSON>_webhook_verify_2024"}]}}, "response": []}, {"name": "Facebook Webhook Handler", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Hub-Signature-256", "value": "sha256=SIGNATURE_HASH"}], "body": {"mode": "raw", "raw": "{\n    \"object\": \"page\",\n    \"entry\": [\n        {\n            \"id\": \"PAGE_ID\",\n            \"time\": 1234567890,\n            \"messaging\": [\n                {\n                    \"sender\": {\n                        \"id\": \"USER_ID\"\n                    },\n                    \"recipient\": {\n                        \"id\": \"PAGE_ID\"\n                    },\n                    \"timestamp\": 1234567890,\n                    \"message\": {\n                        \"mid\": \"message_id\",\n                        \"text\": \"Hello from Facebook!\"\n                    }\n                }\n            ]\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/webhooks/facebook", "host": ["{{base_url}}"], "path": ["api", "webhooks", "facebook"]}}, "response": []}]}, {"name": "Integrations", "item": [{"name": "Get Integration Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/integrations/status", "host": ["{{base_url}}"], "path": ["api", "integrations", "status"]}}, "response": []}, {"name": "Get Integration Channels", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/integrations/channels", "host": ["{{base_url}}"], "path": ["api", "integrations", "channels"]}}, "response": []}, {"name": "Update Integration Channels", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"channels\": {\n        \"instagram\": {\n            \"enabled\": true,\n            \"auto_reply\": true,\n            \"business_hours_only\": false\n        },\n        \"facebook\": {\n            \"enabled\": true,\n            \"auto_reply\": true,\n            \"business_hours_only\": true\n        }\n    }\n}"}, "url": {"raw": "{{base_url}}/api/integrations/channels", "host": ["{{base_url}}"], "path": ["api", "integrations", "channels"]}}, "response": []}]}], "variable": [{"key": "base_url", "value": "https://kortana-ai.loca.lt", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}]}