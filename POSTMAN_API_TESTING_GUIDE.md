# Kortana AI - Postman API Testing Guide

This guide explains how to import and use the Postman collection to test all Kortana AI Instagram Chatbot API endpoints.

## 📦 What's Included

The `postman-collection.json` file contains **80+ API endpoints** organized into the following categories:

### 🔐 Authentication
- User registration and login
- Password reset and OTP verification
- Token management

### 💬 Social Media Management
- Send messages to Instagram/Facebook
- Retrieve messages and conversation history
- Get platform statistics and analytics
- Manage active conversations

### ⚙️ Business Configuration
- Manage business information and services
- Configure agent profile and personality
- Update chatbot prompts and responses

### 📚 Knowledge Base
- Upload and manage knowledge files (PDF, TXT, DOC, DOCX)
- Search knowledge base
- File processing and reprocessing

### 📅 Scheduling System
- Manage appointment time slots
- Configure business hours and weekend settings
- Get available appointment slots

### 🔑 Meta Token Management
- Manage Facebook/Instagram API tokens
- Token verification and validation
- Page information management

### 🔗 Instagram Authentication
- OAuth flow for Instagram integration
- Connection status and token refresh

### 🪝 Webhooks
- Instagram and Facebook webhook handlers
- Webhook verification endpoints

### 🔌 Integrations
- Integration status and channel management
- Platform-specific configurations

## 🚀 Quick Start

### 1. Import the Collection

1. Open Postman
2. Click **Import** button
3. Select **File** tab
4. Choose the `postman-collection.json` file
5. Click **Import**

### 2. Set Up Environment Variables

The collection uses these environment variables:

| Variable | Description | Example Value |
|----------|-------------|---------------|
| `base_url` | API base URL | `https://kortana-ai.loca.lt` |
| `auth_token` | Bearer token for authentication | `1|abc123...` |

**To set up variables:**
1. Click the **Environment** tab in Postman
2. Create a new environment called "Kortana AI"
3. Add the variables above
4. Select the environment before testing

### 3. Authentication Flow

**Step 1: Register a new user**
```
POST {{base_url}}/api/v1/auth/register
```

**Step 2: Login to get auth token**
```
POST {{base_url}}/api/v1/auth/login
```
The login request automatically saves the token to `{{auth_token}}` variable.

**Step 3: Test authenticated endpoints**
All protected endpoints use: `Authorization: Bearer {{auth_token}}`

## 📋 Testing Workflow

### Basic API Testing
1. **Health Check** - Verify API is running
2. **Register/Login** - Get authentication token
3. **Get User Profile** - Verify token works

### Social Media Testing
1. **Send Message** - Test message sending
2. **Get Messages** - Retrieve message history
3. **Get Stats** - View platform analytics

### Business Configuration
1. **Get Business Info** - View current settings
2. **Update Business Info** - Modify business details
3. **Configure Agent Profile** - Set chatbot personality

### Knowledge Base Testing
1. **Upload File** - Add knowledge documents
2. **Search Knowledge** - Test search functionality
3. **Get Stats** - View knowledge base metrics

## 🔧 Configuration Examples

### Business Information
```json
{
    "name": "My Restaurant",
    "type": "Restaurant",
    "description": "Italian cuisine restaurant",
    "specialties": ["Pizza", "Pasta", "Italian"],
    "contact": {
        "phone": "+**********",
        "email": "<EMAIL>",
        "address": "123 Main St, City, State 12345"
    },
    "social_media": {
        "instagram": "@myrestaurant",
        "facebook": "myrestaurant",
        "website": "https://myrestaurant.com"
    }
}
```

### Agent Profile
```json
{
    "agent_type": "Customer Service Assistant",
    "agent_name": "Kortana",
    "agent_gender": "female",
    "personality": "friendly",
    "shop_type": "Restaurant",
    "response_tone": "helpful"
}
```

### Chatbot Prompts
```json
{
    "prompts": {
        "greeting": "Welcome to our restaurant! How can I help you today?",
        "services_inquiry": "We offer amazing Italian cuisine, pizza, and pasta. What interests you?",
        "appointment_prompt": "I can help you make a reservation. When would you like to dine with us?",
        "pricing_info": "Our menu items range from $10-30. Would you like to see our full menu?",
        "location_info": "We're located at 123 Main St. Would you like directions?"
    }
}
```

## 🔍 Testing Tips

### 1. Use Test Scripts
The login request includes a test script that automatically saves the auth token:
```javascript
if (pm.response.code === 200) {
    const response = pm.response.json();
    if (response.data && response.data.token) {
        pm.environment.set('auth_token', response.data.token);
    }
}
```

### 2. File Upload Testing
For knowledge base file uploads, use the form-data body type:
- Key: `file` (type: file)
- Key: `description` (type: text)

### 3. Webhook Testing
Webhook endpoints require specific headers:
- `Content-Type: application/json`
- `X-Hub-Signature-256: sha256=SIGNATURE_HASH`

### 4. Error Handling
All endpoints return consistent error responses:
```json
{
    "status": "error",
    "message": "Error description",
    "data": null
}
```

## 🛠️ Environment Setup

### Local Development
```
base_url = http://localhost:8000
```

### Staging/Production
```
base_url = https://kortana-ai.loca.lt
```

## 📝 Notes

- All authenticated endpoints require `Authorization: Bearer {{auth_token}}`
- File uploads use `multipart/form-data`
- JSON requests use `Content-Type: application/json`
- Webhook endpoints are public (no authentication required)
- Rate limiting may apply to some endpoints

## 🐛 Troubleshooting

### Common Issues
1. **401 Unauthorized** - Check if auth_token is set correctly
2. **422 Validation Error** - Review request body format
3. **404 Not Found** - Verify endpoint URL and method
4. **500 Server Error** - Check server logs for details

### Debug Steps
1. Test health check endpoint first
2. Verify environment variables are set
3. Check request headers and body format
4. Review API response for error details

## 📚 Additional Resources

- API Documentation: Check the codebase for detailed endpoint documentation
- Environment File: `.env` contains configuration values
- Route Definitions: `routes/api.php` shows all available endpoints
