[2025-07-17 11:54:45] local.DEBUG: From: <PERSON><PERSON> <<EMAIL>>
To: <EMAIL>
Subject: Welcome to Laravel
MIME-Version: 1.0
Date: Thu, 17 Jul 2025 11:54:45 +0000
Message-ID: <<EMAIL>>
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Kortana AI</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eaeaea;
        }

        .logo {
            width: 150px;
            margin-bottom: 15px;
        }

        .content {
            padding: 30px 20px;
        }

        .footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #6c757d;
            border-top: 1px solid #eaeaea;
        }

        h1 {
            color: #1a56db;
            margin-bottom: 20px;
        }

        .otp-container {
            background-color: #f0f5ff;
            border-left: 4px solid #1a56db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            text-align: center;
        }

        .otp-code {
            font-size: 24px;
            font-weight: bold;
            color: #1a56db;
            letter-spacing: 5px;
        }

        .button {
            display: inline-block;
            background-color: #1a56db;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-weight: 500;
            margin-top: 20px;
        }

        .social-links {
            margin-top: 20px;
        }

        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #1a56db;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <img src="https://kortana-ai.loca.lt/images/admin-logo.webp" alt="Kortana AI Logo" class="logo">
            <h1>Welcome to Kortana AI</h1>
        </div>

        <div class="content">
            <p>Hello John Doe,</p>

            <p>Thank you for joining Kortana AI! We're excited to have you on board. Your AI assistant is ready to help
                automate your appointment bookings and boost your business engagement.</p>

            <p>To verify your email address, please use the following OTP code:</p>

            <div class="otp-container">
                <div class="otp-code">342981</div>
                <p>This code will expire in 10 minutes.</p>
            </div>

            <p>Once verified, you'll have full access to your AI dashboard where you can customize your assistant and
                start automating your appointment bookings.</p>

            <p>If you have any questions or need assistance, our support team is always ready to help.</p>

            <p>Best regards,<br>The Kortana AI Team</p>
        </div>

        <div class="footer">
            <p>&copy; 2025 Kortana AI. All rights reserved.</p>
            <div class="social-links">
                <a href="#">Twitter</a> |
                <a href="#">Facebook</a> |
                <a href="#">LinkedIn</a>
            </div>
            <p>
                <small>If you didn't create an account with us, please ignore this email.</small>
            </p>
        </div>
    </div>
</body>

</html>
  
[2025-07-17 11:54:45] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to Laravel"} 
[2025-07-17 11:56:23] local.DEBUG: From: Laravel <<EMAIL>>
To: <EMAIL>
Subject: Welcome to Laravel
MIME-Version: 1.0
Date: Thu, 17 Jul 2025 11:56:23 +0000
Message-ID: <<EMAIL>>
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Kortana AI</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eaeaea;
        }

        .logo {
            width: 150px;
            margin-bottom: 15px;
        }

        .content {
            padding: 30px 20px;
        }

        .footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #6c757d;
            border-top: 1px solid #eaeaea;
        }

        h1 {
            color: #1a56db;
            margin-bottom: 20px;
        }

        .otp-container {
            background-color: #f0f5ff;
            border-left: 4px solid #1a56db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            text-align: center;
        }

        .otp-code {
            font-size: 24px;
            font-weight: bold;
            color: #1a56db;
            letter-spacing: 5px;
        }

        .button {
            display: inline-block;
            background-color: #1a56db;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-weight: 500;
            margin-top: 20px;
        }

        .social-links {
            margin-top: 20px;
        }

        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #1a56db;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <img src="https://kortana-ai.loca.lt/images/admin-logo.webp" alt="Kortana AI Logo" class="logo">
            <h1>Welcome to Kortana AI</h1>
        </div>

        <div class="content">
            <p>Hello John Doe,</p>

            <p>Thank you for joining Kortana AI! We're excited to have you on board. Your AI assistant is ready to help
                automate your appointment bookings and boost your business engagement.</p>

            <p>To verify your email address, please use the following OTP code:</p>

            <div class="otp-container">
                <div class="otp-code">816707</div>
                <p>This code will expire in 10 minutes.</p>
            </div>

            <p>Once verified, you'll have full access to your AI dashboard where you can customize your assistant and
                start automating your appointment bookings.</p>

            <p>If you have any questions or need assistance, our support team is always ready to help.</p>

            <p>Best regards,<br>The Kortana AI Team</p>
        </div>

        <div class="footer">
            <p>&copy; 2025 Kortana AI. All rights reserved.</p>
            <div class="social-links">
                <a href="#">Twitter</a> |
                <a href="#">Facebook</a> |
                <a href="#">LinkedIn</a>
            </div>
            <p>
                <small>If you didn't create an account with us, please ignore this email.</small>
            </p>
        </div>
    </div>
</body>

</html>
  
[2025-07-17 11:56:23] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to Laravel"} 
[2025-07-17 11:59:19] local.DEBUG: From: Laravel <<EMAIL>>
To: <EMAIL>
Subject: Welcome to Laravel
MIME-Version: 1.0
Date: Thu, 17 Jul 2025 11:59:19 +0000
Message-ID: <<EMAIL>>
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Kortana AI</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eaeaea;
        }

        .logo {
            width: 150px;
            margin-bottom: 15px;
        }

        .content {
            padding: 30px 20px;
        }

        .footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #6c757d;
            border-top: 1px solid #eaeaea;
        }

        h1 {
            color: #1a56db;
            margin-bottom: 20px;
        }

        .otp-container {
            background-color: #f0f5ff;
            border-left: 4px solid #1a56db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            text-align: center;
        }

        .otp-code {
            font-size: 24px;
            font-weight: bold;
            color: #1a56db;
            letter-spacing: 5px;
        }

        .button {
            display: inline-block;
            background-color: #1a56db;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-weight: 500;
            margin-top: 20px;
        }

        .social-links {
            margin-top: 20px;
        }

        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #1a56db;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <img src="https://kortana-ai.loca.lt/images/admin-logo.webp" alt="Kortana AI Logo" class="logo">
            <h1>Welcome to Kortana AI</h1>
        </div>

        <div class="content">
            <p>Hello John Doe,</p>

            <p>Thank you for joining Kortana AI! We're excited to have you on board. Your AI assistant is ready to help
                automate your appointment bookings and boost your business engagement.</p>

            <p>To verify your email address, please use the following OTP code:</p>

            <div class="otp-container">
                <div class="otp-code">198320</div>
                <p>This code will expire in 10 minutes.</p>
            </div>

            <p>Once verified, you'll have full access to your AI dashboard where you can customize your assistant and
                start automating your appointment bookings.</p>

            <p>If you have any questions or need assistance, our support team is always ready to help.</p>

            <p>Best regards,<br>The Kortana AI Team</p>
        </div>

        <div class="footer">
            <p>&copy; 2025 Kortana AI. All rights reserved.</p>
            <div class="social-links">
                <a href="#">Twitter</a> |
                <a href="#">Facebook</a> |
                <a href="#">LinkedIn</a>
            </div>
            <p>
                <small>If you didn't create an account with us, please ignore this email.</small>
            </p>
        </div>
    </div>
</body>

</html>
  
[2025-07-17 11:59:19] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to Laravel"} 
[2025-07-17 12:01:29] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to KortanaAI"} 
[2025-07-17 12:02:23] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to KortanaAI"} 
[2025-07-17 12:04:58] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to KortanaAI"} 
[2025-07-17 12:10:36] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to KortanaAI"} 
[2025-07-17 12:12:58] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to KortanaAI"} 
[2025-07-17 12:15:04] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to KortanaAI"} 
[2025-07-17 12:20:42] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to KortanaAI"} 
[2025-07-17 12:22:35] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to KortanaAI"} 
[2025-07-17 12:24:05] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to KortanaAI"} 
[2025-07-17 12:28:47] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Reset Password OTP"} 
[2025-07-17 12:29:41] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Password Changed Successfully"} 
[2025-07-17 12:36:39] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to KortanaAI"} 
[2025-07-17 12:37:06] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to KortanaAI"} 
[2025-07-18 05:42:23] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to KortanaAI"} 
[2025-07-18 05:45:41] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to KortanaAI"} 
[2025-07-18 05:46:52] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(883): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(278): route('login')
#2 [internal function]: Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}(Object(Illuminate\\Http\\Request))
#3 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(117): call_user_func(Object(Closure), Object(Illuminate\\Http\\Request))
#4 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(104): Illuminate\\Auth\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#5 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(87): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#6 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(61): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#7 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#8 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\kortana-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\kortana-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#39 {main}
"} 
[2025-07-18 05:51:16] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to KortanaAI"} 
[2025-07-18 05:53:49] local.INFO: Email sent successfully {"to":"<EMAIL>","subject":"Welcome to KortanaAI"} 
